# HeyGem SQLite错误诊断脚本
# 用于诊断和修复HeyGem应用程序的SQLite数据库问题

Write-Host "=== HeyGem SQLite错误诊断工具 ===" -ForegroundColor Green
Write-Host ""

# 1. 检查HeyGem进程状态
Write-Host "1. 检查HeyGem进程状态..." -ForegroundColor Yellow
$heygem_processes = Get-Process -Name "HeyGem" -ErrorAction SilentlyContinue
if ($heygem_processes) {
    Write-Host "✓ 发现 $($heygem_processes.Count) 个HeyGem进程正在运行" -ForegroundColor Green
    $heygem_processes | Select-Object Id, ProcessName, StartTime, WorkingSet | Format-Table
} else {
    Write-Host "✗ 没有发现HeyGem进程" -ForegroundColor Red
}

# 2. 查找HeyGem数据目录
Write-Host "2. 查找HeyGem数据目录..." -ForegroundColor Yellow
$possible_data_paths = @(
    "$env:APPDATA\HeyGem",
    "$env:LOCALAPPDATA\HeyGem",
    "$env:USERPROFILE\.heygem",
    "$env:USERPROFILE\AppData\Roaming\HeyGem",
    "$env:USERPROFILE\AppData\Local\HeyGem",
    "F:\heygem\data",
    "F:\heygem\userData"
)

$found_paths = @()
foreach ($path in $possible_data_paths) {
    if (Test-Path $path) {
        $found_paths += $path
        Write-Host "✓ 找到数据目录: $path" -ForegroundColor Green
    }
}

if ($found_paths.Count -eq 0) {
    Write-Host "✗ 未找到HeyGem数据目录" -ForegroundColor Red
}

# 3. 查找SQLite数据库文件
Write-Host "3. 查找SQLite数据库文件..." -ForegroundColor Yellow
$db_files = @()

# 在找到的数据目录中搜索
foreach ($path in $found_paths) {
    $files = Get-ChildItem -Path $path -Include "*.db", "*.sqlite", "*.sqlite3" -Recurse -ErrorAction SilentlyContinue
    $db_files += $files
}

# 在用户目录中搜索HeyGem相关的数据库文件
$user_db_files = Get-ChildItem -Path $env:USERPROFILE -Include "*.db", "*.sqlite*" -Recurse -ErrorAction SilentlyContinue | 
    Where-Object { $_.Directory.FullName -like "*heygem*" -or $_.Directory.FullName -like "*HeyGem*" }
$db_files += $user_db_files

if ($db_files.Count -gt 0) {
    Write-Host "✓ 找到 $($db_files.Count) 个数据库文件:" -ForegroundColor Green
    foreach ($file in $db_files) {
        Write-Host "  - $($file.FullName) (大小: $([math]::Round($file.Length/1KB, 2)) KB)" -ForegroundColor Cyan
    }
} else {
    Write-Host "✗ 未找到SQLite数据库文件" -ForegroundColor Red
}

# 4. 检查数据库文件完整性
Write-Host "4. 检查数据库文件完整性..." -ForegroundColor Yellow
foreach ($db_file in $db_files) {
    try {
        # 尝试读取文件头来验证是否为有效的SQLite文件
        $bytes = [System.IO.File]::ReadAllBytes($db_file.FullName)
        if ($bytes.Length -ge 16) {
            $header = [System.Text.Encoding]::ASCII.GetString($bytes[0..15])
            if ($header.StartsWith("SQLite format 3")) {
                Write-Host "✓ $($db_file.Name) - 有效的SQLite文件" -ForegroundColor Green
            } else {
                Write-Host "✗ $($db_file.Name) - 无效的SQLite文件头" -ForegroundColor Red
            }
        } else {
            Write-Host "✗ $($db_file.Name) - 文件太小，可能已损坏" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ $($db_file.Name) - 无法读取文件: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 5. 生成修复建议
Write-Host "5. 生成修复建议..." -ForegroundColor Yellow
Write-Host ""
Write-Host "=== 修复建议 ===" -ForegroundColor Green

if ($heygem_processes.Count -gt 0) {
    Write-Host "建议1: 重启HeyGem应用程序" -ForegroundColor Cyan
    Write-Host "  执行命令: Get-Process -Name 'HeyGem' | Stop-Process -Force; Start-Sleep 3; Start-Process 'F:\heygem\HeyGem.exe'"
    Write-Host ""
}

if ($db_files.Count -gt 0) {
    Write-Host "建议2: 备份数据库文件" -ForegroundColor Cyan
    Write-Host "  在尝试修复前，请备份以下文件:"
    foreach ($file in $db_files) {
        Write-Host "  Copy-Item '$($file.FullName)' '$($file.FullName).backup'"
    }
    Write-Host ""
}

Write-Host "建议3: 检查模型添加操作" -ForegroundColor Cyan
Write-Host "  - 确保模型文件路径使用完整路径"
Write-Host "  - 避免在模型名称中使用特殊字符"
Write-Host "  - 确保所有参数都是基本数据类型（字符串、数字、布尔值）"
Write-Host ""

Write-Host "建议4: 如果问题持续存在" -ForegroundColor Cyan
Write-Host "  - 尝试以管理员权限运行HeyGem"
Write-Host "  - 检查Windows事件日志中的相关错误"
Write-Host "  - 考虑重新安装HeyGem应用程序"
Write-Host ""

Write-Host "=== 诊断完成 ===" -ForegroundColor Green
Write-Host "如需更多帮助，请将此诊断结果发送给技术支持。" -ForegroundColor Yellow
