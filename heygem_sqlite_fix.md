# HeyGem SQLite错误诊断和修复指南

## 错误信息
```
Error: Error invoking remote method 'model/addModel': TypeError: SQLite3 can only bind numbers, strings, bigints, buffers, and null
```

## 问题分析

这个错误表明HeyGem应用程序在尝试向SQLite数据库添加模型时，传递了不支持的数据类型。

### 可能的原因：

1. **数据类型问题**：传递了undefined、object、array或其他复杂数据类型
2. **参数绑定错误**：SQL参数绑定时使用了错误的数据格式
3. **数据序列化问题**：复杂对象没有正确序列化为字符串
4. **空值处理**：undefined值没有转换为null

## 解决方案

### 方案1：重启应用程序
```powershell
# 结束所有HeyGem进程
Get-Process -Name "HeyGem" | Stop-Process -Force

# 等待几秒钟
Start-Sleep -Seconds 3

# 重新启动HeyGem
Start-Process "F:\heygem\HeyGem.exe"
```

### 方案2：清理应用数据
```powershell
# 结束HeyGem进程
Get-Process -Name "HeyGem" | Stop-Process -Force

# 查找并备份可能的数据库文件
$possiblePaths = @(
    "$env:APPDATA\HeyGem",
    "$env:LOCALAPPDATA\HeyGem", 
    "$env:USERPROFILE\.heygem",
    "F:\heygem\data"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        Write-Host "Found data directory: $path"
        # 备份数据库文件
        Get-ChildItem -Path $path -Include "*.db", "*.sqlite*" -Recurse | ForEach-Object {
            Copy-Item $_.FullName "$($_.FullName).backup"
            Write-Host "Backed up: $($_.FullName)"
        }
    }
}
```

### 方案3：检查模型数据格式
如果你在添加自定义模型时遇到此错误，请确保：

1. **模型路径**：使用完整的文件路径字符串
2. **模型名称**：使用纯字符串，避免特殊字符
3. **配置参数**：确保所有参数都是基本数据类型

```javascript
// 正确的模型数据格式示例
const modelData = {
    name: "模型名称",           // 字符串
    path: "C:\\path\\to\\model", // 字符串
    type: "video",              // 字符串
    size: 1024,                 // 数字
    enabled: true,              // 布尔值会转换为数字
    description: "模型描述"      // 字符串
};

// 错误的格式（会导致SQLite错误）
const badModelData = {
    name: undefined,            // ❌ undefined
    path: null,                 // ✅ null是可以的
    config: { key: "value" },   // ❌ 对象
    tags: ["tag1", "tag2"]      // ❌ 数组
};
```

### 方案4：数据库修复
如果数据库文件损坏：

```powershell
# 查找SQLite数据库文件
Get-ChildItem -Path "C:\Users" -Recurse -Include "*.db", "*.sqlite*" -ErrorAction SilentlyContinue | 
    Where-Object { $_.Directory.FullName -like "*heygem*" -or $_.Directory.FullName -like "*HeyGem*" }
```

## 预防措施

1. **定期备份**：定期备份HeyGem的数据目录
2. **数据验证**：在添加模型前验证数据格式
3. **版本更新**：保持HeyGem应用程序为最新版本
4. **系统兼容性**：确保系统环境符合要求

## 调试步骤

1. **查看详细错误**：
   - 打开HeyGem的开发者工具（如果可用）
   - 查看控制台错误信息

2. **检查模型文件**：
   - 确认模型文件完整且未损坏
   - 检查文件路径是否正确

3. **测试简单操作**：
   - 尝试添加一个简单的测试模型
   - 逐步增加复杂度

## 联系支持

如果以上方案都无法解决问题，建议：
1. 收集错误日志和系统信息
2. 联系HeyGem技术支持
3. 提供详细的错误重现步骤
